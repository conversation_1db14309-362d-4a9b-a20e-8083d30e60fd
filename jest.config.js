export default {
  preset: 'ts-jest/presets/js-with-ts-esm',
  testEnvironment: 'node',
  extensionsToTreatAsEsm: ['.ts'],
  moduleNameMapper: {
    '^(\\.{1,2}/.*)\\.js$': '$1',
  },
  transform: {
    '^.+\\.tsx?$': [
      'ts-jest',
      {
        useESM: true,
      },
    ],
  },
  // Add this to ensure Je<PERSON> can handle ESM
  transformIgnorePatterns: [
    'node_modules/(?!(@xmldom|fast-xml-parser|xpath)/)'
  ],
};
