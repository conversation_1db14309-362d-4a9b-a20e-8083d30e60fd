{"compilerOptions": {"target": "ES2020", "module": "NodeNext", "moduleResolution": "NodeNext", "esModuleInterop": true, "resolveJsonModule": true, "strict": true, "skipLibCheck": true, "outDir": "dist", "sourceMap": true, "declaration": true, "allowSyntheticDefaultImports": true, "forceConsistentCasingInFileNames": true, "isolatedModules": true, "baseUrl": ".", "paths": {"*": ["node_modules/*"]}}, "include": ["src/**/*"], "exclude": ["node_modules", "dist"]}