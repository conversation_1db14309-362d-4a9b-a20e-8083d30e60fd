package com.jarvis.appium.client;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.jarvis.appium.mcp.client.McpClient;
import com.jarvis.appium.mcp.model.ToolResult;
import com.jarvis.appium.model.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * High-level client for Jarvis Appium MCP server
 * Provides convenient methods for mobile automation
 */
public class JarvisAppiumClient implements AutoCloseable {
    
    private static final Logger logger = LoggerFactory.getLogger(JarvisAppiumClient.class);
    
    private final McpClient mcpClient;
    private final ObjectMapper objectMapper;
    private String currentSessionId;
    private Platform currentPlatform;
    
    public JarvisAppiumClient() {
        this.mcpClient = new McpClient();
        this.objectMapper = new ObjectMapper();
    }
    
    /**
     * Connect to the Jarvis Appium MCP server
     */
    public void connect() throws IOException {
        connect("npx", "jarvis-appium");
    }
    
    /**
     * Connect to the Jarvis Appium MCP server with custom command
     */
    public void connect(String... serverCommand) throws IOException {
        mcpClient.connect(serverCommand);
        logger.info("Connected to Jarvis Appium MCP server");
    }
    
    /**
     * Select the platform for testing
     */
    public void selectPlatform(Platform platform) throws IOException {
        ObjectNode params = objectMapper.createObjectNode();
        params.put("platform", platform.getValue());
        
        ToolResult result = mcpClient.callTool("select_platform", params);
        if (result.getIsError() != null && result.getIsError()) {
            throw new IOException("Failed to select platform: " + result.getTextContent());
        }
        
        this.currentPlatform = platform;
        logger.info("Selected platform: {}", platform);
    }
    
    /**
     * Create a new mobile session
     */
    public SessionInfo createSession(Platform platform) throws IOException {
        return createSession(platform, null);
    }
    
    /**
     * Create a new mobile session with custom capabilities
     */
    public SessionInfo createSession(Platform platform, Map<String, Object> capabilities) throws IOException {
        ObjectNode params = objectMapper.createObjectNode();
        params.put("platform", platform.getValue());
        
        if (capabilities != null && !capabilities.isEmpty()) {
            params.set("capabilities", objectMapper.valueToTree(capabilities));
        }
        
        ToolResult result = mcpClient.callTool("create_session", params);
        if (result.getIsError() != null && result.getIsError()) {
            throw new IOException("Failed to create session: " + result.getTextContent());
        }
        
        this.currentPlatform = platform;
        String resultText = result.getTextContent();
        
        // Extract session ID from result text
        if (resultText != null && resultText.contains("session created successfully with ID: ")) {
            String sessionId = resultText.substring(resultText.indexOf("ID: ") + 4);
            sessionId = sessionId.split("\\n")[0].trim();
            this.currentSessionId = sessionId;
        }
        
        logger.info("Created {} session: {}", platform, resultText);
        return new SessionInfo(currentSessionId, platform, resultText);
    }
    
    /**
     * Create a LambdaTest cloud session
     */
    public SessionInfo createLambdaTestSession(LambdaTestSessionConfig config) throws IOException {
        ObjectNode params = objectMapper.createObjectNode();
        params.put("platform", config.getPlatform().getValue());
        params.put("deviceName", config.getDeviceName());
        params.put("platformVersion", config.getPlatformVersion());
        
        if (config.getApp() != null) {
            params.put("app", config.getApp());
        }
        if (config.getBuildName() != null) {
            params.put("buildName", config.getBuildName());
        }
        if (config.getTestName() != null) {
            params.put("testName", config.getTestName());
        }
        if (config.getLtOptions() != null) {
            params.set("ltOptions", objectMapper.valueToTree(config.getLtOptions()));
        }
        if (config.getCapabilities() != null) {
            params.set("capabilities", objectMapper.valueToTree(config.getCapabilities()));
        }
        
        ToolResult result = mcpClient.callTool("create_lambdatest_session", params);
        if (result.getIsError() != null && result.getIsError()) {
            throw new IOException("Failed to create LambdaTest session: " + result.getTextContent());
        }
        
        this.currentPlatform = config.getPlatform();
        String resultText = result.getTextContent();
        
        logger.info("Created LambdaTest session: {}", resultText);
        return new SessionInfo(currentSessionId, config.getPlatform(), resultText);
    }
    
    /**
     * Upload an app to LambdaTest
     */
    public String uploadAppToLambdaTest(String appPath, String appName) throws IOException {
        ObjectNode params = objectMapper.createObjectNode();
        params.put("appPath", appPath);
        if (appName != null) {
            params.put("appName", appName);
        }
        
        ToolResult result = mcpClient.callTool("upload_app_lambdatest", params);
        if (result.getIsError() != null && result.getIsError()) {
            throw new IOException("Failed to upload app: " + result.getTextContent());
        }
        
        String resultText = result.getTextContent();
        logger.info("Uploaded app to LambdaTest: {}", resultText);
        
        // Extract app URL from result
        if (resultText != null && resultText.contains("lt://")) {
            int startIndex = resultText.indexOf("lt://");
            int endIndex = resultText.indexOf(" ", startIndex);
            if (endIndex == -1) endIndex = resultText.length();
            return resultText.substring(startIndex, endIndex);
        }
        
        return resultText;
    }
    
    /**
     * Generate locators for the current screen
     */
    public List<ElementLocator> generateLocators() throws IOException {
        ToolResult result = mcpClient.callTool("generate_locators", objectMapper.createObjectNode());
        if (result.getIsError() != null && result.getIsError()) {
            throw new IOException("Failed to generate locators: " + result.getTextContent());
        }
        
        String resultText = result.getTextContent();
        logger.debug("Generated locators: {}", resultText);
        
        try {
            JsonNode jsonResult = objectMapper.readTree(resultText);
            JsonNode interactableElements = jsonResult.get("interactableElements");
            
            return objectMapper.convertValue(interactableElements, 
                objectMapper.getTypeFactory().constructCollectionType(List.class, ElementLocator.class));
        } catch (Exception e) {
            throw new IOException("Failed to parse locators response: " + e.getMessage(), e);
        }
    }
    
    /**
     * Find an element using strategy and selector
     */
    public String findElement(LocatorStrategy strategy, String selector) throws IOException {
        ObjectNode params = objectMapper.createObjectNode();
        params.put("strategy", strategy.getValue());
        params.put("selector", selector);
        
        ToolResult result = mcpClient.callTool("appium_find_element", params);
        if (result.getIsError() != null && result.getIsError()) {
            throw new IOException("Failed to find element: " + result.getTextContent());
        }
        
        String resultText = result.getTextContent();
        logger.debug("Found element: {}", resultText);
        
        // Extract element UUID from result
        if (resultText != null && resultText.contains("Element id ")) {
            String elementId = resultText.substring(resultText.indexOf("Element id ") + 11);
            return elementId.trim();
        }
        
        return resultText;
    }
    
    /**
     * Click on an element
     */
    public void clickElement(String elementUUID) throws IOException {
        ObjectNode params = objectMapper.createObjectNode();
        params.put("elementUUID", elementUUID);
        
        ToolResult result = mcpClient.callTool("appium_click", params);
        if (result.getIsError() != null && result.getIsError()) {
            throw new IOException("Failed to click element: " + result.getTextContent());
        }
        
        logger.info("Clicked element: {}", elementUUID);
    }
    
    /**
     * Set text value in an element
     */
    public void setElementValue(String elementUUID, String text) throws IOException {
        ObjectNode params = objectMapper.createObjectNode();
        params.put("elementUUID", elementUUID);
        params.put("text", text);
        
        ToolResult result = mcpClient.callTool("appium_set_value", params);
        if (result.getIsError() != null && result.getIsError()) {
            throw new IOException("Failed to set element value: " + result.getTextContent());
        }
        
        logger.info("Set element value: {} = {}", elementUUID, text);
    }
    
    /**
     * Get text from an element
     */
    public String getElementText(String elementUUID) throws IOException {
        ObjectNode params = objectMapper.createObjectNode();
        params.put("elementUUID", elementUUID);
        
        ToolResult result = mcpClient.callTool("appium_get_text", params);
        if (result.getIsError() != null && result.getIsError()) {
            throw new IOException("Failed to get element text: " + result.getTextContent());
        }
        
        return result.getTextContent();
    }
    
    /**
     * Take a screenshot
     */
    public String takeScreenshot() throws IOException {
        ToolResult result = mcpClient.callTool("appium_screenshot", objectMapper.createObjectNode());
        if (result.getIsError() != null && result.getIsError()) {
            throw new IOException("Failed to take screenshot: " + result.getTextContent());
        }
        
        return result.getTextContent();
    }
    
    /**
     * Generate tests from steps
     */
    public String generateTests(List<String> steps) throws IOException {
        ObjectNode params = objectMapper.createObjectNode();
        params.set("steps", objectMapper.valueToTree(steps));
        
        ToolResult result = mcpClient.callTool("appium_generate_tests", params);
        if (result.getIsError() != null && result.getIsError()) {
            throw new IOException("Failed to generate tests: " + result.getTextContent());
        }
        
        return result.getTextContent();
    }
    
    // Getters
    public String getCurrentSessionId() {
        return currentSessionId;
    }
    
    public Platform getCurrentPlatform() {
        return currentPlatform;
    }
    
    public boolean isConnected() {
        return mcpClient.isConnected();
    }
    
    @Override
    public void close() throws IOException {
        mcpClient.close();
    }
}
