## How To Test Android App Bundle

Google has released the [Android App Bundle](https://developer.android.com/platform/technology/app-bundle/) feature.
An `.aab` file is generated by the feature, which we are supposed to upload to the Google Play Store. We can manage the `.aab` file via CLI using the official [bundletool](https://developer.android.com/studio/command-line/bundletool) which is available from [bundletool](https://github.com/google/bundletool). [The guide](https://developer.android.com/guide/app-bundle/) also help us to understand the feature.

We can get distributed apk files from the `.aab` file via the CLI. Using the generated files, we can test against the release module. Since Appium 1.9.2, you can Appium tests against an `.apks` file using UiAutomator2 driver. [1](https://github.com/appium/appium-adb/pull/367) and [2](https://github.com/appium/appium-base-driver/pull/271) are PRs for the feature.

## How to run tests

1. Export `bundletool.jar` in your path
    - Appium looks for `bundletool.jar` in your local environemnt. Make sure you can find the path with `which 'bundletool.jar'`. If you can't find it, please set the path correctly.
    - Please make sure the bundletool version is above 1.6.0
2. Generate the `.apks` file from the `.aab` file
    - The `.aab` is available over Android Studio 3.2
    - You must sign correctly when you generate `.apks` from `.aab`. This step requires data signing.

```bash
$ java -jar apks/bundletool.jar build-apks \
    --bundle apks/release/release/app.aab \ # A generated aab file
    --output apks/AppBundleSample.apks \    # An apks file you'd like to out put to
    --ks apks/sign \                        # Signing keystore
    --ks-key-alias key0 \                   # Alias of the keytstore
    --ks-pass pass:kazucocoa \              # Password of the keystore
    --overwrite                             # Overwrite any existing apks files
```

3. Use the path to the `.apks` file as your `app` capability.

```ruby
capabilities = {
    platformName: :android,
    automationName: 'uiautomator2',
    platformVersion: '8.1',
    deviceName: 'Android Emulator',
    app: "path/to/your.apks",   # This line is important
    fullReset: true,
    ...
}

core = ::Appium::Core.for capabilities: capabilities
driver = core.start_driver
```

You can find another way to get test APKs in https://developer.android.com/guide/app-bundle/

You could also install `.apks` bundles via Install App command like below.

```ruby
# Ruby
driver.execute_script 'mobile: installApp', {appPath: 'path/to/your.apks'}
```

## Tips
### Make `bundletool.jar` executable

Make sure the bundletool is executable.
`$ chmod 655 /path/to/bundletool.jar` can make it executable, for example.

### Test with different languages

Set `fullReset: true` if you would like to test against the app using different languages' resources.

Appium only installs the minimum set of resources, following the behavior of the appbundle feature. For example, if a device's language is set of English, Appium will only install the `en` resource. The installed apk will have no Japanese resources.

In orfer to force re-install with a different set of language resources, specify `fullReset: true`

## An example project

- https://github.com/KazuCocoa/AppBundleSample
