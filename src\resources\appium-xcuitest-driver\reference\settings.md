---
hide:
  - toc

title: Settings
---

The XCUITest driver supports Appium's [Settings API](https://appium.io/docs/en/latest/guides/settings/).
Along with the common settings, the following driver-specific settings are available:

| <div style="width:14em">Name</div> | Type | Description |
| --- | --- | --- |
| `elementResponseAttributes` | `string` | Comma-separated list of element attribute names to be included into findElement response. By default only element UUID is present there, but it is also possible to add the following items: `name`, `text`, `rect`, `enabled`, `displayed`, `selected`, `attribute/<element_attribute_name>`. It is required that `shouldUseCompactResponses` setting is set to `false` in order for this one to apply. |
| `shouldUseCompactResponses` | `boolean` | Used in combination with `elementResponseAttributes` setting. If set to `false` then the findElement response is going to include the items enumerated in `elementResponseAttributes` setting. `true` by default |
| `screenshotQuality` | `int` | See the description of the corresponding capability. |
| `mjpegServerFramerate` | `int` | The maximum count of screenshots per second taken by the MJPEG screenshots broadcaster. Must be in range 1..60. `10` by default |
| `mjpegScalingFactor` | `float` | The percentage value used to apply downscaling on the screenshots generated by the MJPEG screenshots broadcaster. Must be in range 1..100. `100` is by default, which means that screenshots are not downscaled. |
| `mjpegServerScreenshotQuality` | `int` | The percentage value used to apply lossy JPEG compression on the screenshots generated by the MJPEG screenshots broadcaster. Must be in range 1..100. `25` is by default, which means that screenshots are compressed to the quarter of their original quality. |
| <s>`customSnapshotTimeout`</s> | `float` | *Removed since XCUITest driver v8.0.0*. Set how much time in float seconds is allowed to resolve a single accessibility snapshot with custom attributes. _Snapshots_ are mainly used for page source generation, XML lookup and custom attributes retrieval (these are visibility and accessibility ones). It might be necessary to increase this value if the actual page source is very large and contains hundreds of UI elements. Defaults to 15 seconds. Since Appium 1.19.1 if this timeout expires and no custom snapshot could be made then WDA tries to calculate the missing attributes using its own algorithms, so setting this value to zero might speed up, for example, page source retrieval, but for the cost of preciseness of some element attributes. |
| `waitForIdleTimeout` | `float` | Has the same meaning as corresponding capability (see above) |
| `animationCoolOffTimeout` | `float` | The amount of time in float seconds to wait until the application under test does not have any active animations. This check is usually applied after each automation action that is supposed to change the state of the application under test, like `click` one, and blocks XCTest until the transition of the tested application to a new state completes or the cool off timeout occurs. The default value is `2` (seconds). Setting it to zero disables animation checks completely. |
| `snapshotMaxDepth` | `int` | Changes the value of maximum depth for traversing elements source tree. It may help to prevent out of memory or timeout errors while getting the elements source tree, but it might restrict the depth of source tree. Please consider restricting this value if you observed an error like _Timed out snapshotting com.apple.testmanagerd..._ message or _Cannot get 'xml' source of the current application_ in your Appium log since they are possibly timeout related. A part of elements source tree might be lost if the value was too small. Defaults to `50` |
| `useFirstMatch` | `boolean` | Enabling this setting makes single element lookups faster, but there is the known [problem](https://github.com/appium/appium/issues/10101) related to nested elements lookup. Defaults to `false`. |
| `reduceMotion` | `boolean` | Changes the 'reduce motion' preference of accessibility feature. Defaults to `false` |
| `defaultActiveApplication` | `string` | Sets the hint for active application selection. This helps WebDriverAgent to select the current application if there are multiple items in the active applications list and the desired one is also one of them. The setting is particularly useful for split-screen apps automation. Defaults to `auto`, which makes WebDriverAgent to select the application whose element is located at `screenPoint` location or a single item from the active apps list if the length of this list is equal to one. |
| `activeAppDetectionPoint` | `string` | Defines the coordinates of the current screen point. WebDriverAgent uses this point to detect the active application if multiple application are active on the screen. The format of this value is `x,y`, where x and y are float or integer numbers representing valid screen coordinates. Setting this value to a point outside the actual screen coordinates might corrupt WebDriverAgent functionality. By default the screen point coordinates equal to 20% of the minimum screen dimension each, e.g. `MIN(w, h) * 0.2, MIN(w, h) * 0.2` |
| `includeNonModalElements` | `boolean` | Whether returns all of elements including no modal dialogs on iOS 13+. It fixes [cannot find elements on nested modal presentations](https://github.com/appium/appium/issues/13227), but it might make visibility attributes unreliable. You could also enable `shouldUseTestManagerForVisibilityDetection` setting (defaults to `false`) or `simpleIsVisibleCheck` capability to improve the visibility detection. This issue may happen between iOS 13.0 to 13.2 (Xcode 11.0 to 11.2). The query issued in `includeNonModalElements` returns `nil` with newer iOS/Xcode versions and Appium/WDA return proper elements three without this setting being used. Defaults to `false`. |
| `acceptAlertButtonSelector` | `string` | Allows to customize accept alert button selector. It helps you to handle an arbitrary element as accept button in `accept alert` command. The selector should be a valid [class chain](https://github.com/facebookarchive/WebDriverAgent/wiki/Class-Chain-Queries-Construction-Rules) expression, where the search root is the alert element itself. The default button location algorithm is used if the provided selector is wrong or does not match any element. Example: ```**/XCUIElementTypeButton[`label CONTAINS[c] 'accept'`]``` |
| `dismissAlertButtonSelector` | `string` | Allows to customize dismiss alert button selector. It helps you to handle an arbitrary element as dismiss button in `dismiss alert` command. The selector should be a valid [class chain](https://github.com/facebookarchive/WebDriverAgent/wiki/Class-Chain-Queries-Construction-Rules) expression, where the search root is the alert element itself. The default button location algorithm is used if the provided selector is wrong or does not match any element. Example: ```**/XCUIElementTypeButton[`label CONTAINS[c] 'dismiss'`]``` |
| `screenshotOrientation` | `string` | Adjust screenshot orientation for iOS. Appium tries to return a screenshot and adjust its orientation properly using internal heuristics, but sometimes it does not work, especially in landscape mode. The actual screenshot orientation depends on various factors such as OS versions, model versions and whether this is a real or simulator device. This option allows you to enforce the given image orientation. Acceptable values: `auto` (default), `portrait`, `portraitUpsideDown`, `landscapeRight`, `landscapeLeft`. |
| `boundElementsByIndex` | `boolean` | Whether to look up elements with [`allElementsBoundByAccessibilityElement`](https://developer.apple.com/documentation/xctest/xcuielementquery/1500816-allelementsboundbyaccessibilitye) (default) or [`allElementsBoundByIndex`](https://developer.apple.com/documentation/xctest/xcuielementquery/1500945-allelementsboundbyindex). [This Stack Overflow topic](https://stackoverflow.com/questions/49307513/meaning-of-allelementsboundbyaccessibilityelement) explains the differences. Defaults to `false`. |
| `keyboardAutocorrection` | `boolean` | Changes the 'Auto-Correction' preference in _Keyboards_ setting. Defaults to `false`. |
| `keyboardPrediction` | `boolean` | Changes the 'Predictive' preference in _Keyboards_ setting. Defaults to `false`. |
| `nativeWebTap` | `boolean` | See the description of the corresponding capability. |
| `nativeWebTapStrict` | `boolean` | See the description of the corresponding capability. |
| `nativeWebTapTabBarVisibility` | `enum` | Bypass finding whether the existence of the _**tab bar**_ before tapping on the element. It could make native web tap faster. If it's `visible`, tab bar offset will be added without checking the existence of the tab bar. It's `invisible`, the tab bar offset will be `zero`. If you want to leave Appium to check and measure the tab bar offset, unset or set `detect`. Only applicable if `nativeWebTap` and `nativeWebTapStrict` are enabled. Unset by default. |
| `nativeWebTapSmartAppBannerVisibility` | `enum` | The same as `nativeWebTapTabBarVisibility`, this keyword will bypass finding whether the existence of the _**smart app banner**_. |
| `safariTabBarPosition` | `string` | Handle offset of Safari tab bar in `nativeWebTap` enabled interactions. If `platformVersion` was greater than or equal to 15 and iPhone device, the value is `bottom` by default. Otherwise `top`. When the value is `top`, Appium considers offset as the bar length. iOS 15+ environment can customize the bar position in the settings app, so please adjust the offset with this. Acceptable values: `bottom`, `top` |
| `useJSONSource` | `boolean` | See the description of the corresponding capability. |
| `pageSourceExcludedAttributes` | `string` | One or more comma-separated attribute names to be excluded from the output. It might be sometimes helpful to exclude, for example, the `visible` attribute, to significantly speed-up page source retrieval. Attributes which can be excluded with `useJSONSource` are frame, enabled, visible, accessible and focused. Defaults to an empty string. Example: `"visible,accessible"` |
| `maxTypingFrequency` | `int` | Maximum frequency of keystrokes for typing and clear. If your tests are failing because of typing errors, you may want to adjust this. Defaults to `60` keystrokes per minute. |
| `respectSystemAlerts` | `boolean` | Currently we detect the app under test as active if XCTest returns XCUIApplicationStateRunningForeground state for it. In case the app under test is covered by a system alert from the Springboard app this approach might be confusing as we cannot interact with it unless an alert is properly handled. If this setting is set to true (by default it is false) then it forces WDA to verify the presence of alerts shown by Springboard and return the latter while performing the automated app detection. It affects the performance of active app detection, but might be more convenient for writing test scripts (e.g. eliminates the need of proactive switching between system and custom apps). Also, this behavior emulates the legacy active application detection logic before version 6 of the driver. |
| `webScreenshotMode` | `native` or `page` or `viewport` | Defines the screenshoting logic if the current context is set to a web one. The default value is `native`, which makes the driver to take screenshots from WDA, e.g. the whole device screen including status bars. The `page` mode tries to retrieve the screenshot of the whole active web page, while the `viewport` one only retrieves a shot of the visible viewport. |
| `useClearTextShortcut` | `boolean` | Whether to use the fastest operation (using IOHIDEvent) to clear texts. In headless mode, simulator's keyboard won't show up anymore after clearing texts using this approach in some cases (see [this issue](https://github.com/appium/appium/issues/20727) for more details). Defaults to true |
| `limitXPathContextScope` | `boolean` | Due to historical reasons XCUITest driver limits scopes of element context-based searches to the parent element. This means a request like `findElement(By.xpath, "//XCUIElementTypeButton").findElement(By.xpath, "./..")` would always fail, because the driver only collects descendants of the `XCUIElementTypeButton` element for the destination XML source. The `limitXPathContextScope` setting being set to `false` changes that default behavior, so the collected page source includes the whole page source XML where `XCUIElementTypeButton` node is set as the search context. With that setting disabled the search query above should not fail anymore. Although, you must be careful while building XPath requests for context-based searches with the `limitXPathContextScope` setting being set to `false`. A request like `findElement(By.xpath, "//XCUIElementTypeAlert").findElement(By.xpath, "//XCUIElementTypeButton")` would ignore the current context and search for `XCUIElementTypeButton` through the whole page source. Use `.` notation to correct that behavior and only find `XCUIElementTypeButton` nodes which are descendants of the `XCUIElementTypeAlert` node: `findElement(By.xpath, "//XCUIElementTypeAlert").findElement(By.xpath, ".//XCUIElementTypeButton")`. |
| `skipDocumentsContainerCheck` | `boolean` | Whether to apply special handling for the `documents` container type in file management such as pushing/pulling to/from real devices (`false`, the default value), or to treat them in the same way as other container types (`true`). For certain applications having this setting enabled helps to workaround documents upload issues if the `UIFileSharingEnabled` flag is not active in the application manifest.|
| `autoClickAlertSelector` | `string` | Allows providing a custom [class chain](./locator-strategies.md) selector, which is used to automatically locate an element inside an alert hierarchy, and click it as soon as a new alert is detected. If the provided locator string is invalid then an error is thrown. Providing an empty string disables the feature. This setting has priority over `autoAcceptAlerts` or `autoDismissAlerts` capabilities, e.g. if both are enabled then the destination element to tap on would be selected only based on the provided selector value. If an alert is active, but no element matches the provided selector, then no click would be performed. If two or more elements match the provided selector then the very first matched element is clicked. The selector lookup is performed starting from the root element of the matched alert hierarchy (usually XCUIElementTypeAlert). |
| `includeHittableInPageSource` | `boolean` | Determines whether the [`hittable`](https://developer.apple.com/documentation/xctest/xcuielement/ishittable) attribute should be included in the page source XML tree. This value reflects whether an element is currently interactive from XCTest’s perspective. Disabled by default due to potential performance impact on large UI hierarchies. Defaults to `false`. |
