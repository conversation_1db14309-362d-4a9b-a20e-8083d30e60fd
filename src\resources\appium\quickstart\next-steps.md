---
hide:
  - toc

title: Next Steps
---

Now that you've successfully set up your system for Android automation and run a simple test,
you'll want to continue exploring this documentation. In particular, these are good guides and
reference materials especially for beginners:

- The [Ecosystem](../ecosystem/index.md) page: browse the available drivers, clients, plugins, and tools
- [Managing Appium Drivers and Plugins](../guides/managing-exts.md)
- [Capabilities](../guides/caps.md)
- [Settings](../guides/settings.md)

You'll also find that the [Appium Inspector](https://github.com/appium/appium-inspector) is an
indispensable tool for writing Appium tests, as it enables visual inspection of apps and
helps you to discover element locators for use in your test scripts.

You might also take advantage of one of the many online Appium courses available to you.

Good luck and have fun!
