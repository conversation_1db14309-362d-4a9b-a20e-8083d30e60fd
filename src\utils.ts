export function checkIsValidElementId(str: string) {
  //   const isValidUUID =
  //     /^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[1-5][0-9a-fA-F]{3}-[89abAB][0-9a-fA-F]{3}-[0-9a-fA-F]{12}$/.test(
  //       str
  //     );
  //   if (!isValidUUID) {
  //     throw new Error(
  //       'Given id is not a valid element id. Call find_element_tool to fetch the correct uuid of the element'
  //     );
  //   }
  return true;
}
