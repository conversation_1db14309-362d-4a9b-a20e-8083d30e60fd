---
hide:
  - toc

title: Locator Strategies
---

The XCUITest driver supports several location strategies in the native context. The following table
lists them in performance order (the first one is the fastest one):

| <div style="width:14em">Name</div> | Description | Speed Ranking | Example |
| --- | --- | --- | --- |
| `className` | Performs search by element's `type` [attribute](element-attributes.md). The full list of supported XCUIElement type names could be found in the official XCTest [documentation on XCUIElementType](https://developer.apple.com/documentation/xctest/xcuielementtype) | ⭐⭐⭐⭐⭐ | `XCUIElementTypeButton` |
| `id`, `name`, `accessibility id` | All these locator types are synonyms and internally get transformed into search by element's `name` [attribute](element-attributes.md). | ⭐⭐⭐⭐⭐  | `my name` |
| `-ios predicate string` | This strategy is mapped to the native XCTest predicate locator. Check the [NSPredicate cheat sheet](https://academy.realm.io/posts/nspredicate-cheatsheet/) for more details on how to build effective predicate expressions. All the supported element [attributes](element-attributes.md) could be used in these expressions. | ⭐⭐⭐⭐⭐  | `(name == 'done' OR value == 'done') AND type IN {'XCUIElementTypeButton', 'XCUIElementTypeKey'}` |
| `-ios class chain` | This strategy is mapped to the native XCTest predicate locator, but with respect to the actual element tree hierarchy. Such locators are basically a supertype of `-ios predicate string`. Read [Class Chain Queries Construction Rules](https://github.com/facebookarchive/WebDriverAgent/wiki/Class-Chain-Queries-Construction-Rules) for more details on how to build such locators. | ⭐⭐⭐⭐ | ```**/XCUIElementTypeCell[$name == 'done' OR value == 'done'$]/XCUIElementTypeButton[-1]``` |
| `xpath` | For elements lookup using the Xpath strategy the driver uses the same XML tree that is generated by the page source API. This means such locators are the slowest (sometimes up to 10x slower) in comparison to the ones above, which all depend on native XCTest primitives, but are the most flexible. Use Xpath locators only if there is no other way to locate the given element. Only Xpath 1.0 is supported. | ⭐⭐ | `//XCUIElementTypeButton[@value=\"Regular\"]/parent::*` |

Also, consider checking the [How To Achieve The Best Lookup Performance](https://github.com/facebookarchive/WebDriverAgent/wiki/How-To-Achieve-The-Best-Lookup-Performance) article.
