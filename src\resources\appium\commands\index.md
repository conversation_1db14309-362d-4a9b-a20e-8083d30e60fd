---
hide:
  - toc

title: Intro to Commands
---

Here you can find various commands exposed by the main Appium module through its base driver, as
well as the commands available in several plugins.

!!! note

    The Appium base driver only exposes a few commands, as it is not meant to be used on its own.
    Please refer to the documentation of your [Appium driver](../ecosystem/drivers.md) to learn
    about the commands it exposes, and then check your [Appium client](../ecosystem/clients.md)
    documentation for the exact syntax of that command.

The command listings can be found here:

* [Base Driver](./base-driver.md)
* [Execute Driver Plugin](./execute-driver-plugin.md)
* [Images Plugin](./images-plugin.md)
* [Inspector](https://github.com/appium/appium-inspector/tree/main/plugins)
* [Relaxed Caps Plugin](./relaxed-caps-plugin.md)
* [Storage Plugin](./storage-plugin.md)
* [Universal XML Plugin](./universal-xml-plugin.md)
