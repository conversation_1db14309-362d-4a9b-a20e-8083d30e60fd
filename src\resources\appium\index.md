---
hide:
  - navigation
  - toc

title: Welcome
---
<style>
  .md-typeset h1,
  .appium-sponsor-thanks {
    display: none;
  }
</style>

<div style="text-align: center">
  <img src="assets/images/appium-logo-horiz.png" style="max-width: 400px;" />
</div>

Welcome to the Appium documentation! Appium is an open-source project and ecosystem of related
software, designed to facilitate UI automation of many app platforms, including mobile (iOS,
Android, Tizen), browser (Chrome, Firefox, Safari), desktop (macOS, Windows), TV (Roku, tvOS,
Android TV, Samsung), and more!


<div style="text-align: center; margin-top: 2rem; font-style: italic;">
  Appium is extremely grateful for the support of its key partners! (Learn more about our
  sponsorship program and contributor compensation scheme <a
  href="https://github.com/appium/appium/blob/master/GOVERNANCE.md#sponsorship">here</a>)
  <div class="homepageSponsors">
    <div class="homepageSponsor">
      <a href="https://www.browserstack.com/browserstack-appium?utm_campaigncode=701OW00000AoUTQYA3&utm_medium=partnered&utm_source=appium">
        <img src="assets/images/sponsor-logo-browserstack-dark.png#only-dark" style="width: 220px;" />
        <img src="assets/images/sponsor-logo-browserstack-light.png#only-light" style="width: 220px;" />
      </a>
    </div>
  </div>
</div>

## Explore the Documentation

<div class="grid cards" markdown>

-   Check out the [__Introduction__](./intro/index.md) to make sure you understand the key concepts
-   Go through the [__Quickstart__](./quickstart/index.md) to get set up and run a basic Android test
-   Visit the [__Ecosystem__](./ecosystem/index.md) page for a list of drivers, clients and plugins you may want to use
-   Refer to the [__CLI Reference__](./cli/index.md) for using Appium from the command line
-   See the [__Command Reference__](./commands/index.md) for a list of commands exposed by Appium and plugins
-   Read the different [__Guides__](./guides/migrating-1-to-2.md) for a variety of instructions, tips and tricks
-   Check out various third-party [__Resources__](./resources.md) to explore Appium around the web
-   For creating your own Appium extensions, see the [__Developer__](./developing/index.md) documentation
-   For contributions to Appium itself, refer to the [__Contributing__](./contributing.md) page

</div>
