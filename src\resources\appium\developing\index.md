---
hide:
  - toc

title: Intro to Development
---

Appium 2 is built with a modular structure, which means that Appium extensions (drivers and plugins)
are decoupled from the main Appium module, and you only need to install the extensions that you
want to use. This modular structure also unlocks the ability to develop entirely new extensions!

This section of the Appium documentation is intended to help aspiring developers with creating their
own Appium extension:

* For creating a driver, see the [Build Drivers](./build-drivers.md) page
* For creating a plugin, take a look at the [Build Plugins](build-plugins.md) page
* Drivers and plugins both need documentation, so check out the [Build Documentation](./build-docs.md) page
* For creating a doctor check, see the [Building Doctor Checks](./build-doctor-checks.md) page
