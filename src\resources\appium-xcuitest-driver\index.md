---
hide:
  - navigation
  - toc

title: Welcome
---
<style>
  .md-typeset h1 {
    display: none;
  }
</style>

<div style="text-align: center">
  <img src="assets/images/appium-plus-xctest.png" style="max-width: 500px;" />
</div>

Welcome to the Appium XCUITest Driver documentation! The XCUITest driver is a test automation
framework for iOS, iPadOS and tvOS devices, enabling automated black-box testing of native, hybrid
and WebKit web apps, on both emulators and real devices. 

The XCUITest driver is part of the Appium test automation tool. For information on Appium itself,
please visit [the Appium documentation](https://appium.io).

## Explore the Documentation

<div class="grid cards" markdown>

-   Check out the [__Overview__](./overview.md) to learn how the driver works
-   Go through the [__Installation__](./installation/index.md) steps to get set up
-   Follow the [__Device Preparation__](./preparation/index.md) instructions to configure your test device
-   Browse the [__Reference__](./reference/scripts.md) documentation for everything exposed by the driver
-   Read the different [__Guides__](./guides/parallel-tests.md) for a variety of instructions, tips and tricks
-   For contributions to the driver, refer to the [__Contributing__](./contributing.md) page

</div>
