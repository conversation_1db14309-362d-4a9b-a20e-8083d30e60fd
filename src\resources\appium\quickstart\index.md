---
hide:
  - toc

title: Quickstart Intro
---

Let's get up and running with Appium! To successfully use this quickstart, it's recommended that
you first have read the [Introduction](../intro/index.md), so that you understand the concepts involved in
running Appium and writing Appium scripts.

The basic plan for this quickstart is as follows:

1. Install Appium
1. Install an Appium driver and its dependencies
    - This guide provides instructions for the [UiAutomator2 driver](https://github.com/appium/appium-uiautomator2-driver)
1. Install an Appium client library in your language of choice
    - This guide contains options for JavaScript, Python, Java, Ruby, and .NET
1. Write and run a simple Appium automation script using a sample application

### Requirements

Before getting started, make sure your system satisfies the
[requirements](../quickstart/requirements.md) for running the Appium server. Additional requirements
will be discussed in conjunction with installing the UiAutomator2 driver. The guide also assumes
you have basic command line proficiency on your platform, for example being able to run commands, set
and persist environment variables, etc...

Now you're ready to get started! So head on over to [Installing Appium](./install.md).
