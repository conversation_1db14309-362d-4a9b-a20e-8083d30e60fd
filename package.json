{"name": "jarvis-appium", "version": "1.0.0", "type": "module", "main": "src/index.ts", "bin": {"jarvis-appium": "dist/index.js"}, "scripts": {"build": "rimraf dist && tsc && npm run copy-docs", "copy-docs": "mkdir -p dist/tools/documentation/uploads && cp -f src/tools/documentation/uploads/documents.json dist/tools/documentation/uploads/documents.json 2>/dev/null || echo 'No documents.json found, run npm run index-docs first'", "start": "node dist/index.js", "start:stdio": "node src/index.js", "start:sse": "node src/index.js --sse", "start:sse:port": "node src/index.js --sse --port=", "dev": "npx fastmcp dev src/index.js", "inspect": "npx fastmcp inspect src/index.js", "test": "NODE_OPTIONS=--experimental-vm-modules jest", "test:locators": "NODE_OPTIONS=--experimental-vm-modules jest src/tests/generate-all-locators.test.ts", "lint": "eslint src --ext .ts", "lint:fix": "eslint src --ext .ts --fix", "format": "prettier --write \"src/**/*.{ts,js,json}\"", "format:check": "prettier --check \"src/**/*.{ts,js,json}\"", "check": "npm run lint && npm run format:check", "index-docs": "node dist/scripts/simple-index-documentation.js", "query-docs": "node dist/scripts/simple-query-documentation.js", "prepublish": "npm run build"}, "author": "", "license": "MIT", "description": "Intelligent MCP server providing AI assistants with powerful tools and resources for Appium mobile automation", "dependencies": {"@modelcontextprotocol/sdk": "^1.11.0", "@xenova/transformers": "^2.17.2", "@xmldom/xmldom": "^0.9.8", "appium-uiautomator2-driver": "^4.2.3", "appium-xcuitest-driver": "^9.6.0", "fast-xml-parser": "^5.2.3", "fastmcp": "^1.23.2", "form-data": "^4.0.3", "langchain": "^0.3.27", "lodash": "^4.17.21", "rimraf": "^6.0.1", "xpath": "^0.0.34", "zod": "^3.24.3"}, "devDependencies": {"@jest/globals": "^29.7.0", "@types/form-data": "^2.2.1", "@types/jest": "^29.5.12", "@types/lodash": "^4.17.17", "@types/node": "^22.15.18", "@typescript-eslint/eslint-plugin": "^8.34.0", "@typescript-eslint/parser": "^8.34.0", "eslint": "^9.28.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-prettier": "^5.4.1", "jest": "^29.7.0", "prettier": "^3.5.3", "ts-jest": "^29.1.2", "ts-node": "^10.9.2", "typescript": "^5.8.3"}}